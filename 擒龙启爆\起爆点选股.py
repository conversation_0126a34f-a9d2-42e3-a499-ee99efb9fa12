{关键变量计算}
VAR0:=(CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60))*100;
VAR3:=SMA(VAR0,3,1);
VAR1:=SMA(VAR3,4,1)-10;
VAR4:=(HHV(HIGH,60)-CLOSE)/(HHV(HIGH,60)-LLV(LOW,60))*100;
VAR5:=SMA(VAR4,3,1);
VAR2:=SMA(VAR5,4,1)-90;

{关键起爆点计算逻辑}
VAR02:=REF(LOW,1);
VAR03:=SMA(ABS(LOW-VAR02),13,1)/SMA(MAX(LOW-VAR02,0),13,1)*100;
VAR04:=EMA(IF(CLOSE*1.2,VAR03*13,VAR03/13),13);
VAR05:=LLV(LOW,34);
VAR6:=HHV(VAR04,34);
VAR7:=IF(LLV(LOW,56),1,0);
VAR8:=EMA(IF(LOW<=VAR05,(VAR4+VAR6*2)/2,0),3)/618*VAR7;
AA:=VAR8>REF(VAR8,1);
DR:=100;
ZRQ:=3;
DJ:=REF(LLV(L,100),3);
ZD:=REFDATE(DJ,DATE);
XG0:=L=ZD;
XGA:=AA&&XG0;
XG1:=XGA>REF(XGA,1);
起爆点:=XG1>REF(XG1,1);
FILTER(起爆点=1,5);