# 黑马启动选股公式
# 基于金牛暴起指标中的黑马启动条件

# 时间条件（可根据需要调整）
SJKS:=DATE>=1220816;
SJJS:=DATE<=1250801;
SJTJ:= SJKS AND SJJS;

# 计算金线王指标
FA1:=ABS(((3.48*CLOSE+HIGH+LOW)/4-EMA(CLOSE,23))/EMA(CLOSE,23));
FA2:=DMA(((2.15*CLOSE+LOW+HIGH)/4),FA1);
金线王:=EMA(FA2,200)*1.118;

# 黑马启动条件
条件:=(C-REF(C,1))/REF(C,1)*100>8;  # 当日涨幅大于8%
金K线:=CROSS(C,金线王) AND 条件;      # 收盘价突破金线王且涨幅大于8%

# 选股条件
黑马启动选股:金K线 AND SJTJ;

# 副图显示
黑马启动:金K线*11,LINETHICK2;
DRAWTEXT(金K线 AND SJTJ,7,'黑马启动'),COLORRED;
DRAWICON(金K线,5,11);

# 输出选股结果
黑马启动选股;
