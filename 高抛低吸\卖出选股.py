# 卖出选股公式
# 基于高抛低吸指标中的卖出条件

# 参数设置
N1:=21;
N2:=8;

# 计算相关指标
VAR1:=3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
VAR2:=(CLOSE-LLV(LOW,26))/(HHV(HIGH,26)-LLV(LOW,26))*100;
VAR3:=SMA(SMA(VAR2,3,1),3,1);
VAR4:=EMA(VAR3,5);
VAR5:=LLV(LOW,26);
VAR6:=HHV(HIGH,34);
VAR7:=EMA((CLOSE-VAR5)/(VAR6-VAR5)*4,4)*25;
VAR8:=(2*C+H+L)/4;
VAR9:=LLV(LOW,N1);
VAR10:=HHV(HIGH,N2);
VAR2W:=100-100*(HHV(HIGH,14)-CLOSE)/(HHV(HIGH,14)-LLV(LOW,14));
MW:= EMA(VAR2W,3);
VAR3W:=EMA(VAR2W,7);
M1:= EMA(VAR3W,5);
MB1:=CROSS(MW,M1) AND M1<20;
MG1:=IF(CROSS(M1,MW) AND REF(MW,1)>80,80 ,0);

# 核心指标计算
MJ:=EMA((VAR8-VAR9)/(VAR10-VAR9)*100,9);
TM:=EMA(0.667*REF(MJ,1)+0.333*MJ,2);

# 卖出条件：秘籍指标从上方向下跌破80
CROSS(80,MJ);
