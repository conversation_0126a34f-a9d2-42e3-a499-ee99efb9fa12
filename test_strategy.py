#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试策略代码语法
"""

def test_strategy_syntax():
    try:
        # 导入策略模块
        exec(open('策略/妖底确定买入高抛卖出策略.py', encoding='utf-8').read())
        print("✓ 策略代码语法检查通过")
        return True
    except SyntaxError as e:
        print(f"✗ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

if __name__ == "__main__":
    test_strategy_syntax()
