# 妖底确定选股公式
# 基于金牛暴起指标中的妖底确定条件

# 时间条件（可根据需要调整）
SJKS:=DATE>=1220816;
SJJS:=DATE<=1250801;
SJTJ:= SJKS AND SJJS;

# 计算相关指标
C1:=((MA(C,30)-L)/MA(C,60))*200;
M2:=SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100;
G1:=FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5);
TU:=C/MA(C,40)<0.74;
SMMA:=EMA(EMA(C,5),5);
IM:=EMA(C,5)- REF(EMA(C,5),1);
TSMMA:=SMMA - REF(SMMA,1);
DIVMA:= ABS(EMA(C,5)- SMMA);
TDJ:=(H-L)/REF(C,1)>0.05;
ET:=(IM+TSMMA)/2;
TDF:= POW(DIVMA,1)*POW(ET,3);
NTDF:=TDF/HHV(ABS(TDF),5*3);
YUL:=COUNT(TDJ,5)>1;
QD:=TU AND TDJ AND YUL;
QR:=CROSS(NTDF,-0.9);
BD:=FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10);
XG:=FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10);

# 妖底确定条件
COUNT(XG,13)>=1 AND BD;
