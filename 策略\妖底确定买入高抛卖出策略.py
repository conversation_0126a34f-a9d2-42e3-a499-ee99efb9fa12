"""
妖底确定买入高抛卖出量化策略
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位
3. 收盘前检测持仓股票是否触发卖出信号
4. 如有卖出信号则清仓
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class YaodiGaopaoStrategy:
    def __init__(self, initial_capital=100000, position_ratio=0.1):
        """
        初始化策略
        :param initial_capital: 初始资金
        :param position_ratio: 单次买入仓位比例（十分之一）
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position_ratio = position_ratio
        self.positions = {}  # 持仓字典 {股票代码: {'shares': 股数, 'cost': 成本价}}
        self.trade_log = []  # 交易记录
        
    def calculate_yaodi_signal(self, data):
        """
        计算妖底确定买入信号
        :param data: 股票数据DataFrame，包含OHLCV等字段
        :return: 是否满足妖底确定条件
        """
        try:
            # 计算相关指标
            C = data['close']
            H = data['high'] 
            L = data['low']
            V = data['volume']
            
            # C1指标
            ma30 = C.rolling(30).mean()
            ma60 = C.rolling(60).mean()
            C1 = ((ma30 - L) / ma60) * 200
            
            # M2指标
            price_change = np.maximum(C - C.shift(1), 0)
            abs_change = np.abs(C - C.shift(1))
            M2 = (price_change.rolling(7).mean() / abs_change.rolling(7).mean()) * 100
            
            # G1条件
            G1 = (M2.shift(1) < 20) & (M2 > M2.shift(1))
            
            # TU条件：超跌状态
            ma40 = C.rolling(40).mean()
            TU = C / ma40 < 0.74
            
            # SMMA指标
            ema5 = C.ewm(span=5).mean()
            SMMA = ema5.ewm(span=5).mean()
            IM = ema5 - ema5.shift(1)
            TSMMA = SMMA - SMMA.shift(1)
            DIVMA = np.abs(ema5 - SMMA)
            
            # TDJ条件：振幅大于5%
            TDJ = (H - L) / C.shift(1) > 0.05
            
            # ET和相关计算
            ET = (IM + TSMMA) / 2
            TDF = np.power(DIVMA, 1) * np.power(ET, 3)
            NTDF = TDF / TDF.abs().rolling(15).max()
            
            # YUL条件
            YUL = TDJ.rolling(5).sum() > 1
            
            # 启动条件
            QD = TU & TDJ & YUL
            
            # 确定条件
            QR = (NTDF > -0.9) & (NTDF.shift(1) <= -0.9)
            
            # 波段条件（简化）
            BD_condition1 = (C1 > 20) | (C > C.shift(1))
            BD = BD_condition1 & QD.shift(1)
            
            # 选股条件（简化MACD条件）
            XG_condition = QD.shift(1) & (QR | (C > C.shift(1)))
            XG = XG_condition
            
            # 妖底确定条件
            yaodi_signal = (XG.rolling(13).sum() >= 1) & BD
            
            return yaodi_signal.iloc[-1] if len(yaodi_signal) > 0 else False
            
        except Exception as e:
            print(f"计算妖底确定信号时出错: {e}")
            return False
    
    def calculate_sell_signal(self, data):
        """
        计算高抛低吸卖出信号
        :param data: 股票数据DataFrame
        :return: 是否满足卖出条件
        """
        try:
            C = data['close']
            H = data['high']
            L = data['low']
            
            # 参数设置
            N1, N2 = 21, 8
            
            # 计算VAR8（典型价格）
            VAR8 = (2 * C + H + L) / 4
            
            # 计算VAR9和VAR10
            VAR9 = L.rolling(N1).min()
            VAR10 = H.rolling(N2).max()
            
            # 计算秘籍指标MJ
            MJ = ((VAR8 - VAR9) / (VAR10 - VAR9) * 100).ewm(span=9).mean()
            
            # 卖出条件：MJ从上方向下跌破80
            sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)
            
            return sell_signal.iloc[-1] if len(sell_signal) > 0 else False
            
        except Exception as e:
            print(f"计算卖出信号时出错: {e}")
            return False
    
    def execute_buy(self, stock_code, price, date):
        """
        执行买入操作
        :param stock_code: 股票代码
        :param price: 买入价格
        :param date: 交易日期
        """
        # 计算买入金额（十分之一仓位）
        buy_amount = self.current_capital * self.position_ratio
        shares = int(buy_amount / price / 100) * 100  # 按手买入
        
        if shares > 0:
            cost = shares * price
            self.current_capital -= cost
            
            if stock_code in self.positions:
                # 已有持仓，计算平均成本
                old_shares = self.positions[stock_code]['shares']
                old_cost = self.positions[stock_code]['cost']
                new_shares = old_shares + shares
                new_avg_cost = (old_shares * old_cost + shares * price) / new_shares
                self.positions[stock_code] = {'shares': new_shares, 'cost': new_avg_cost}
            else:
                # 新建仓位
                self.positions[stock_code] = {'shares': shares, 'cost': price}
            
            # 记录交易
            self.trade_log.append({
                'date': date,
                'stock_code': stock_code,
                'action': 'BUY',
                'price': price,
                'shares': shares,
                'amount': cost,
                'capital': self.current_capital
            })
            
            print(f"{date} 买入 {stock_code}: {shares}股, 价格: {price:.2f}, 金额: {cost:.2f}")
    
    def execute_sell(self, stock_code, price, date):
        """
        执行卖出操作（清仓）
        :param stock_code: 股票代码
        :param price: 卖出价格
        :param date: 交易日期
        """
        if stock_code in self.positions:
            shares = self.positions[stock_code]['shares']
            cost_price = self.positions[stock_code]['cost']
            sell_amount = shares * price
            
            self.current_capital += sell_amount
            
            # 计算盈亏
            profit = (price - cost_price) * shares
            profit_rate = (price - cost_price) / cost_price * 100
            
            # 记录交易
            self.trade_log.append({
                'date': date,
                'stock_code': stock_code,
                'action': 'SELL',
                'price': price,
                'shares': shares,
                'amount': sell_amount,
                'profit': profit,
                'profit_rate': profit_rate,
                'capital': self.current_capital
            })
            
            # 清除持仓
            del self.positions[stock_code]
            
            print(f"{date} 卖出 {stock_code}: {shares}股, 价格: {price:.2f}, "
                  f"盈亏: {profit:.2f} ({profit_rate:.2f}%)")
    
    def run_strategy(self, stock_data_dict, start_date, end_date):
        """
        运行策略
        :param stock_data_dict: 股票数据字典 {股票代码: DataFrame}
        :param start_date: 开始日期
        :param end_date: 结束日期
        """
        print(f"开始运行策略，时间范围: {start_date} 到 {end_date}")
        print(f"初始资金: {self.initial_capital}")
        
        # 这里应该是主要的策略执行逻辑
        # 由于需要实际的股票数据，这里提供框架
        print("策略框架已建立，需要接入实际数据源")
        
    def get_performance_summary(self):
        """
        获取策略表现摘要
        """
        if not self.trade_log:
            return "暂无交易记录"
        
        df = pd.DataFrame(self.trade_log)
        
        # 计算总收益
        total_return = self.current_capital - self.initial_capital
        return_rate = (total_return / self.initial_capital) * 100
        
        # 统计交易次数
        buy_count = len(df[df['action'] == 'BUY'])
        sell_count = len(df[df['action'] == 'SELL'])
        
        # 计算胜率（如果有卖出记录）
        if sell_count > 0:
            win_trades = len(df[(df['action'] == 'SELL') & (df['profit'] > 0)])
            win_rate = (win_trades / sell_count) * 100
        else:
            win_rate = 0
        
        summary = f"""
策略表现摘要:
=============
初始资金: {self.initial_capital:,.2f}
当前资金: {self.current_capital:,.2f}
总收益: {total_return:,.2f}
收益率: {return_rate:.2f}%
买入次数: {buy_count}
卖出次数: {sell_count}
胜率: {win_rate:.2f}%
当前持仓数: {len(self.positions)}
        """
        
        return summary

# 使用示例
if __name__ == "__main__":
    # 创建策略实例
    strategy = YaodiGaopaoStrategy(initial_capital=100000, position_ratio=0.1)
    
    print("妖底确定买入高抛卖出策略已初始化")
    print("策略说明:")
    print("1. 每日收盘前使用妖底确定选股筛选买入标的")
    print("2. 买入当前十分之一仓位")
    print("3. 收盘前检测持仓股票是否触发卖出信号")
    print("4. 如有卖出信号则清仓")
    print("\n需要接入实际股票数据源才能运行完整策略")
