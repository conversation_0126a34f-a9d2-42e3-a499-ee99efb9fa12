"""
妖底确定买入高抛卖出量化策略
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位
3. 收盘前检测持仓股票是否触发卖出信号
4. 如有卖出信号则清仓
"""

from datetime import datetime, timedelta
import math

class YaodiGaopaoStrategy:
    def __init__(self, initial_capital=100000, position_ratio=0.1):
        """
        初始化策略
        :param initial_capital: 初始资金
        :param position_ratio: 单次买入仓位比例（十分之一）
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position_ratio = position_ratio
        self.positions = {}  # 持仓字典 {股票代码: {'shares': 股数, 'cost': 成本价}}
        self.trade_log = []  # 交易记录
        
    def calculate_yaodi_signal(self, data):
        """
        计算妖底确定买入信号
        :param data: 股票数据字典，包含close, high, low, volume等列表
        :return: 是否满足妖底确定条件
        """
        try:
            # 获取数据
            close_prices = data.get('close', [])
            high_prices = data.get('high', [])
            low_prices = data.get('low', [])

            if len(close_prices) < 75:  # 需要足够的历史数据
                return False

            # 简化的妖底确定条件判断
            current_price = close_prices[-1]
            prev_price = close_prices[-2] if len(close_prices) > 1 else current_price

            # 计算40日均线（简化）
            ma40_period = min(40, len(close_prices))
            ma40 = sum(close_prices[-ma40_period:]) / ma40_period

            # TU条件：超跌状态
            TU = current_price / ma40 < 0.74

            # TDJ条件：当日振幅大于5%
            current_high = high_prices[-1]
            current_low = low_prices[-1]
            TDJ = (current_high - current_low) / prev_price > 0.05

            # YUL条件：近5日内有超过1天满足振幅条件（简化）
            recent_days = min(5, len(close_prices) - 1)
            high_amplitude_days = 0
            for i in range(recent_days):
                if i + 1 < len(close_prices):
                    day_high = high_prices[-(i+1)]
                    day_low = low_prices[-(i+1)]
                    day_prev = close_prices[-(i+2)] if i + 2 < len(close_prices) else close_prices[-(i+1)]
                    if (day_high - day_low) / day_prev > 0.05:
                        high_amplitude_days += 1

            YUL = high_amplitude_days > 1

            # 启动条件
            QD = TU and TDJ and YUL

            # 简化的确定条件：价格上涨
            price_rising = current_price > prev_price

            # 妖底确定信号
            yaodi_signal = QD and price_rising

            return yaodi_signal

        except Exception as e:
            print(f"计算妖底确定信号时出错: {e}")
            return False
    
    def calculate_sell_signal(self, data):
        """
        计算高抛低吸卖出信号
        :param data: 股票数据字典
        :return: 是否满足卖出条件
        """
        try:
            close_prices = data.get('close', [])
            high_prices = data.get('high', [])
            low_prices = data.get('low', [])

            if len(close_prices) < 21:  # 需要足够的历史数据
                return False

            # 参数设置
            N1, N2 = 21, 8

            # 计算典型价格VAR8
            current_close = close_prices[-1]
            current_high = high_prices[-1]
            current_low = low_prices[-1]
            VAR8 = (2 * current_close + current_high + current_low) / 4

            # 计算VAR9（N1日最低价）
            N1_period = min(N1, len(low_prices))
            VAR9 = min(low_prices[-N1_period:])

            # 计算VAR10（N2日最高价）
            N2_period = min(N2, len(high_prices))
            VAR10 = max(high_prices[-N2_period:])

            # 计算秘籍指标MJ（简化EMA计算）
            if VAR10 == VAR9:  # 避免除零
                return False

            raw_mj = (VAR8 - VAR9) / (VAR10 - VAR9) * 100

            # 简化的EMA计算：使用最近几天的平均值
            ema_period = min(9, len(close_prices))
            recent_values = []
            for i in range(ema_period):
                if i < len(close_prices):
                    day_close = close_prices[-(i+1)]
                    day_high = high_prices[-(i+1)]
                    day_low = low_prices[-(i+1)]
                    day_var8 = (2 * day_close + day_high + day_low) / 4

                    # 重新计算对应的VAR9和VAR10
                    day_n1_period = min(N1, i + 1)
                    day_n2_period = min(N2, i + 1)

                    # 修复列表切片语法
                    if i > 0:
                        day_var9 = min(low_prices[-(i+day_n1_period):-i])
                        day_var10 = max(high_prices[-(i+day_n2_period):-i])
                    else:
                        day_var9 = min(low_prices[-day_n1_period:])
                        day_var10 = max(high_prices[-day_n2_period:])

                    if day_var10 != day_var9:
                        day_mj = (day_var8 - day_var9) / (day_var10 - day_var9) * 100
                        recent_values.append(day_mj)

            if not recent_values:
                return False

            MJ = sum(recent_values) / len(recent_values)

            # 获取前一天的MJ值（简化）
            if len(recent_values) > 1:
                prev_MJ = recent_values[1]  # 前一天的值
            else:
                prev_MJ = MJ

            # 卖出条件：MJ从上方向下跌破80
            sell_signal = (prev_MJ > 80) and (MJ <= 80)

            return sell_signal

        except Exception as e:
            print(f"计算卖出信号时出错: {e}")
            return False
    
    def execute_buy(self, stock_code, price, date):
        """
        执行买入操作
        :param stock_code: 股票代码
        :param price: 买入价格
        :param date: 交易日期
        """
        # 计算买入金额（十分之一仓位）
        buy_amount = self.current_capital * self.position_ratio
        shares = int(buy_amount / price / 100) * 100  # 按手买入
        
        if shares > 0:
            cost = shares * price
            self.current_capital -= cost
            
            if stock_code in self.positions:
                # 已有持仓，计算平均成本
                old_shares = self.positions[stock_code]['shares']
                old_cost = self.positions[stock_code]['cost']
                new_shares = old_shares + shares
                new_avg_cost = (old_shares * old_cost + shares * price) / new_shares
                self.positions[stock_code] = {'shares': new_shares, 'cost': new_avg_cost}
            else:
                # 新建仓位
                self.positions[stock_code] = {'shares': shares, 'cost': price}
            
            # 记录交易
            self.trade_log.append({
                'date': date,
                'stock_code': stock_code,
                'action': 'BUY',
                'price': price,
                'shares': shares,
                'amount': cost,
                'capital': self.current_capital
            })
            
            print(f"{date} 买入 {stock_code}: {shares}股, 价格: {price:.2f}, 金额: {cost:.2f}")
    
    def execute_sell(self, stock_code, price, date):
        """
        执行卖出操作（清仓）
        :param stock_code: 股票代码
        :param price: 卖出价格
        :param date: 交易日期
        """
        if stock_code in self.positions:
            shares = self.positions[stock_code]['shares']
            cost_price = self.positions[stock_code]['cost']
            sell_amount = shares * price
            
            self.current_capital += sell_amount
            
            # 计算盈亏
            profit = (price - cost_price) * shares
            profit_rate = (price - cost_price) / cost_price * 100
            
            # 记录交易
            self.trade_log.append({
                'date': date,
                'stock_code': stock_code,
                'action': 'SELL',
                'price': price,
                'shares': shares,
                'amount': sell_amount,
                'profit': profit,
                'profit_rate': profit_rate,
                'capital': self.current_capital
            })
            
            # 清除持仓
            del self.positions[stock_code]
            
            print(f"{date} 卖出 {stock_code}: {shares}股, 价格: {price:.2f}, "
                  f"盈亏: {profit:.2f} ({profit_rate:.2f}%)")
    
    def run_strategy(self, stock_data_dict, start_date, end_date):
        """
        运行策略
        :param stock_data_dict: 股票数据字典 {股票代码: DataFrame}
        :param start_date: 开始日期
        :param end_date: 结束日期
        """
        print(f"开始运行策略，时间范围: {start_date} 到 {end_date}")
        print(f"初始资金: {self.initial_capital}")
        
        # 这里应该是主要的策略执行逻辑
        # 由于需要实际的股票数据，这里提供框架
        print("策略框架已建立，需要接入实际数据源")
        
    def get_performance_summary(self):
        """
        获取策略表现摘要
        """
        if not self.trade_log:
            return "暂无交易记录"

        # 计算总收益
        total_return = self.current_capital - self.initial_capital
        return_rate = (total_return / self.initial_capital) * 100

        # 统计交易次数
        buy_count = sum(1 for trade in self.trade_log if trade['action'] == 'BUY')
        sell_count = sum(1 for trade in self.trade_log if trade['action'] == 'SELL')

        # 计算胜率（如果有卖出记录）
        if sell_count > 0:
            win_trades = sum(1 for trade in self.trade_log
                           if trade['action'] == 'SELL' and trade.get('profit', 0) > 0)
            win_rate = (win_trades / sell_count) * 100
        else:
            win_rate = 0

        summary = f"""
策略表现摘要:
=============
初始资金: {self.initial_capital:,.2f}
当前资金: {self.current_capital:,.2f}
总收益: {total_return:,.2f}
收益率: {return_rate:.2f}%
买入次数: {buy_count}
卖出次数: {sell_count}
胜率: {win_rate:.2f}%
当前持仓数: {len(self.positions)}
        """

        return summary

# 使用示例
if __name__ == "__main__":
    # 创建策略实例
    strategy = YaodiGaopaoStrategy(initial_capital=100000, position_ratio=0.1)
    
    print("妖底确定买入高抛卖出策略已初始化")
    print("策略说明:")
    print("1. 每日收盘前使用妖底确定选股筛选买入标的")
    print("2. 买入当前十分之一仓位")
    print("3. 收盘前检测持仓股票是否触发卖出信号")
    print("4. 如有卖出信号则清仓")
    print("\n需要接入实际股票数据源才能运行完整策略")
