"""
妖底确定买入高抛卖出量化策略 - 聚宽平台版本
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位
3. 收盘前检测持仓股票是否触发卖出信号
4. 如有卖出信号则清仓
"""

# 导入聚宽函数库
from jqdata import *
import math

# 初始化函数，设定基准等等
def initialize(context):
    # 设定沪深300作为基准
    set_benchmark('000300.XSHG')
    # 开启动态复权模式(真实价格)
    set_option('use_real_price', True)
    # 输出内容到日志
    log.info('妖底确定买入高抛卖出策略初始化')
    
    # 股票类每笔交易时的手续费设置
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003, close_commission=0.0003, min_commission=5), type='stock')
    
    # 策略参数
    g.position_ratio = 0.1  # 单次买入仓位比例（十分之一）
    g.max_positions = 10  # 最大持仓数量
    
    # 运行函数设置
    # 收盘前30分钟运行选股和交易
    run_daily(trade_strategy, time='14:30', reference_security='000300.XSHG')
    # 收盘后运行
    run_daily(after_market_close, time='after_close', reference_security='000300.XSHG')

def get_holding_stocks(context):
    """
    安全地获取当前持仓股票列表，避免访问空position产生警告
    """
    holding_stocks = []
    for stock in context.portfolio.positions:
        position = context.portfolio.positions[stock]
        if position.closeable_amount > 0:
            holding_stocks.append(stock)
    return holding_stocks

def calculate_yaodi_signal(context, stock):
    """
    计算妖底确定买入信号
    :param context: 聚宽上下文
    :param stock: 股票代码
    :return: 是否满足妖底确定条件
    """
    try:
        # 获取75天的历史数据
        hist_data = get_bars(stock, count=75, unit='1d', 
                           fields=['close', 'high', 'low', 'volume'])
        
        if len(hist_data) < 75:
            return False
            
        close_prices = hist_data['close']
        high_prices = hist_data['high']
        low_prices = hist_data['low']
        
        # 当前价格和前一日价格
        current_price = close_prices[-1]
        prev_price = close_prices[-2] if len(close_prices) > 1 else current_price
        
        # 计算40日均线
        ma40 = close_prices[-40:].mean()
        
        # TU条件：超跌状态（收盘价/40日均线 < 0.74）
        TU = current_price / ma40 < 0.74
        
        # TDJ条件：当日振幅大于5%
        current_high = high_prices[-1]
        current_low = low_prices[-1]
        TDJ = (current_high - current_low) / prev_price > 0.05
        
        # YUL条件：近5日内有超过1天满足振幅条件
        high_amplitude_days = 0
        for i in range(min(5, len(close_prices) - 1)):
            day_high = high_prices[-(i+1)]
            day_low = low_prices[-(i+1)]
            day_prev = close_prices[-(i+2)] if i + 2 < len(close_prices) else close_prices[-(i+1)]
            if (day_high - day_low) / day_prev > 0.05:
                high_amplitude_days += 1
        
        YUL = high_amplitude_days > 1
        
        # 启动条件
        QD = TU and TDJ and YUL
        
        # 简化的确定条件：价格上涨
        price_rising = current_price > prev_price
        
        # 妖底确定信号
        yaodi_signal = QD and price_rising

        # 调试信息（只在信号为True时输出，避免日志过多）
        if yaodi_signal:
            log.info(f"{stock} 妖底确定信号: TU={TU}, TDJ={TDJ}, YUL={YUL}, 价格上涨={price_rising}")

        return yaodi_signal
        
    except Exception as e:
        log.error(f"计算妖底确定信号时出错 {stock}: {e}")
        return False

def calculate_sell_signal(context, stock):
    """
    计算高抛低吸卖出信号
    :param context: 聚宽上下文
    :param stock: 股票代码
    :return: 是否满足卖出条件
    """
    try:
        # 获取30天的历史数据
        hist_data = get_bars(stock, count=30, unit='1d', 
                           fields=['close', 'high', 'low'])
        
        if len(hist_data) < 21:
            return False
            
        close_prices = hist_data['close']
        high_prices = hist_data['high']
        low_prices = hist_data['low']
        
        # 参数设置
        N1, N2 = 21, 8
        
        # 计算当前典型价格VAR8
        current_close = close_prices[-1]
        current_high = high_prices[-1]
        current_low = low_prices[-1]
        VAR8_current = (2 * current_close + current_high + current_low) / 4
        
        # 计算前一日典型价格
        if len(close_prices) > 1:
            prev_close = close_prices[-2]
            prev_high = high_prices[-2]
            prev_low = low_prices[-2]
            VAR8_prev = (2 * prev_close + prev_high + prev_low) / 4
        else:
            VAR8_prev = VAR8_current
        
        # 计算VAR9（N1日最低价）和VAR10（N2日最高价）
        N1_period = min(N1, len(low_prices))
        N2_period = min(N2, len(high_prices))
        VAR9 = low_prices[-N1_period:].min()
        VAR10 = high_prices[-N2_period:].max()
        
        # 避免除零
        if VAR10 == VAR9:
            return False
        
        # 计算当前和前一日的MJ值（简化EMA为直接计算）
        MJ_current = (VAR8_current - VAR9) / (VAR10 - VAR9) * 100
        MJ_prev = (VAR8_prev - VAR9) / (VAR10 - VAR9) * 100
        
        # 卖出条件：MJ从上方向下跌破80
        sell_signal = (MJ_prev > 80) and (MJ_current <= 80)
        
        return sell_signal
        
    except Exception as e:
        log.error(f"计算卖出信号时出错 {stock}: {e}")
        return False

def get_stock_pool(context):
    """
    获取股票池（A股主要股票，排除ST、停牌等）
    """
    try:
        # 获取所有A股股票
        stocks = get_all_securities(['stock']).index.tolist()
        
        # 过滤条件
        current_data = get_current_data()
        filtered_stocks = []
        
        for stock in stocks:
            # 排除ST股票、停牌股票
            if (not current_data[stock].is_st and 
                not current_data[stock].paused and
                current_data[stock].last_price > 0):
                filtered_stocks.append(stock)
        
        # 限制股票池大小，避免计算量过大
        return filtered_stocks[:500]  # 取前500只股票
        
    except Exception as e:
        log.error(f"获取股票池时出错: {e}")
        return []

def trade_strategy(context):
    """
    主要交易策略函数
    """
    log.info('开始执行交易策略')
    
    # 1. 检查当前持仓，执行卖出逻辑
    check_sell_signals(context)
    
    # 2. 如果持仓数量未达到上限，执行买入逻辑
    current_holdings = get_holding_stocks(context)
    current_positions = len(current_holdings)

    if current_positions < g.max_positions:
        check_buy_signals(context)
    
    log.info(f'策略执行完成，当前持仓数量: {current_positions}')

def check_sell_signals(context):
    """
    检查卖出信号
    """
    # 获取当前持仓股票
    holding_stocks = get_holding_stocks(context)

    # 对持有的股票检查卖出信号
    for stock in holding_stocks:
        try:
            if calculate_sell_signal(context, stock):
                log.info(f"触发卖出信号，清仓 {stock}")
                # 清仓
                order_target(stock, 0)
        except Exception as e:
            log.error(f"检查卖出信号时出错 {stock}: {e}")

def check_buy_signals(context):
    """
    检查买入信号
    """
    # 获取股票池
    stock_pool = get_stock_pool(context)
    log.info(f"股票池大小: {len(stock_pool)}")

    # 当前可用资金
    available_cash = context.portfolio.available_cash

    # 单次买入金额（十分之一仓位）
    single_position_value = context.portfolio.total_value * g.position_ratio
    log.info(f"可用资金: {available_cash:.2f}, 单次买入金额: {single_position_value:.2f}")

    buy_candidates = []
    
    # 获取当前实际持仓的股票列表
    current_holdings = get_holding_stocks(context)

    # 遍历股票池，寻找买入信号
    checked_count = 0
    for stock in stock_pool:
        try:
            checked_count += 1
            # 如果已经持有该股票，跳过
            if stock in current_holdings:
                continue

            # 检查妖底确定信号
            if calculate_yaodi_signal(context, stock):
                current_price = get_current_data()[stock].last_price
                if current_price > 0:
                    buy_candidates.append((stock, current_price))
                    log.info(f"找到买入候选: {stock}, 价格: {current_price:.2f}")

            # 限制检查数量，避免超时
            if checked_count >= 100:  # 只检查前100只股票
                break

        except Exception as e:
            log.error(f"检查买入信号时出错 {stock}: {e}")
            continue

    log.info(f"检查了 {checked_count} 只股票，找到 {len(buy_candidates)} 个买入候选")
    
    # 按价格排序，优先买入低价股（可根据需要调整排序逻辑）
    buy_candidates.sort(key=lambda x: x[1])
    
    # 执行买入（限制单次最多买入3只股票）
    bought_count = 0
    for stock, price in buy_candidates:
        if available_cash >= single_position_value and bought_count < 3:
            log.info(f"触发买入信号，买入 {stock}，价格: {price:.2f}")
            # 买入指定金额
            order_value(stock, single_position_value)
            available_cash -= single_position_value
            bought_count += 1
        else:
            break

## 收盘后运行函数
def after_market_close(context):
    """
    收盘后运行函数
    """
    log.info(f'收盘后运行时间: {context.current_dt.time()}')
    
    # 获得当天所有成交记录
    trades = get_trades()
    for _trade in trades.values():
        log.info(f'成交记录: {_trade}')
    
    # 输出当前持仓信息
    positions = context.portfolio.positions
    total_value = context.portfolio.total_value
    available_cash = context.portfolio.available_cash
    
    # 获取实际持仓股票
    holding_stocks = get_holding_stocks(context)

    log.info(f'当前总资产: {total_value:.2f}')
    log.info(f'可用资金: {available_cash:.2f}')
    log.info(f'持仓数量: {len(holding_stocks)}')

    # 输出持仓详情
    for stock in holding_stocks:
        position = positions[stock]
        current_price = get_current_data()[stock].last_price
        profit_loss = (current_price - position.avg_cost) * position.closeable_amount
        profit_rate = (current_price - position.avg_cost) / position.avg_cost * 100
        log.info(f'持仓 {stock}: 数量={position.closeable_amount}, '
                f'成本={position.avg_cost:.2f}, 现价={current_price:.2f}, '
                f'盈亏={profit_loss:.2f} ({profit_rate:.2f}%)')
    
    log.info('一天结束')
    log.info('##############################################################')
