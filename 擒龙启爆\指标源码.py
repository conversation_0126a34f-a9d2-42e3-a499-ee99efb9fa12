{有效日期}
SJ:=1250631;
YXRQ:=IF(DATE<=SJ,1,0);
关注微信公众号:SJ,NODRAW;
AI投研图灵室:SJ-DATE,NODRAW;
{DRAWTEXT_FIX(MOD(ROUND(FROMOPEN),2)=0,0.5,0,0, '【限时返场】关注AI投研图灵室'),COLORRED;}
HAN:=YXRQ;
DRAWGBK((CLOSE>1),RGB(0,0,0),RGB(0,0,2),0,0,0);
L1A:=LLV(L,10);
起爆点0:=(DCLOSE-REF(C,1))/REF(C,1)*100;
XG:=CROSS(起爆点0,20)||CROSS(起爆点0,18);
L3:=IF(XG,O,L1A*0.999)LINETHICK5,COLORYELLOW;
YY:=STICKLINE(XG,L3,L3,20,0),COLORGRAY,LINETHICK5;
MA6:=MA(C,6);MA12:=MA(C,12);MA18:=MA(C,18);
YY1:=O<MA6&&C>MA6;YY2:=O<MA12&&C>MA12;
YY3:=O<MA18&&C>MA18;
YY4:=C/O>=1.02;YY5:=H>=HHV(H,10);
YY6:=MA6>=REF(MA6,1);
YYY:=YY1&&YY2&&YY3&&YY4&&YY5&&YY6;
STICKLINE(YY,0,100,9999,0)*HAN,COLOR000000;
STICKLINE(YY,50,100,2,0)*HAN,COLOR0000FF;
STICKLINE(YY,60,100,4,0)*HAN,COLOR0000FF;
STICKLINE(YY,70,100,6,0)*HAN,COLOR0000FF;
STICKLINE(YY,80,100,7,0)*HAN,COLOR0000FF;
STICKLINE(YY,90,100,8,0)*HAN,COLOR0000FF;
STICKLINE(YY,70,100,1,0)*HAN,COLOR00FFFF;
STICKLINE(YY,80,100,2,0)*HAN,COLOR00FFFF;
STICKLINE(YY,90,100,3,0)*HAN,COLOR00FFFF;
STICKLINE(YY,35,45,9,0)*HAN,COLOR00FFFF;
DRAWTEXT(YY,YY*50,'←启爆火焰'),COLOR0000FF;
VAR0:=(CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60))*100;
VAR3:=SMA(VAR0,3,1);VAR1:=SMA(VAR3,4,1)-10,NODRAW;
VAR4:=(HHV(HIGH,60)-CLOSE)/(HHV(HIGH,60)-LLV(LOW,60))*100;
VAR5:=SMA(VAR4,3,1);VAR2:=SMA(VAR5,4,1)-90;
STICKLINE(REF(VAR1,1)<VAR1,VAR1,REF(VAR1,1),3,0),COLOR0000FF;
STICKLINE(REF(VAR1,1)>VAR1,VAR1,REF(VAR1,1),3,0),COLOR00FF00;
VAR02:=REF(LOW,1);VAR03:=SMA(ABS(LOW-VAR02),13,1)/SMA(MAX(LOW-VAR02,0),13,1)*100;
VAR04:=EMA(IF(CLOSE*1.2,VAR03*13,VAR03/13),13);VAR05:=LLV(LOW,34);VAR6:=HHV(VAR04,34);
VAR7:=IF(LLV(LOW,56),1,0);VAR8:=EMA(IF(LOW<=VAR05,(VAR4+VAR6*2)/2,0),3)/618*VAR7;
AA:=VAR8>REF(VAR8,1);DR:=100;ZRQ:=3;DJ:=REF(LLV(L,100),3);ZD:=REFDATE(DJ,DATE);
XG0:=L=ZD;XGA:=AA&&XG0,COLORRED,LINETHICK2;XG1:=XGA>REF(XGA,1);
起爆点:=XG1>REF(XG1,1)COLORRED,LINETHICK2;
STICKLINE(FILTER(起爆点>=1,5),45,0,4,0),COLORRED;
DRAWTEXT(FILTER(起爆点=1,5),36,'起爆点'),COLORRED;
STICKLINE(起爆点,40,25,1,0),COLORYELLOW;
STICKLINE(起爆点,25,10,2,0),COLOR00FF00;
STICKLINE(起爆点,10,0,3,0),COLORBLUE;
STICKLINE(起爆点,25,15,5,0),COLORRED;
STICKLINE(起爆点,15,6,4,0),COLORLIGREEN;
STICKLINE(起爆点,6,0,6,0),COLORGREEN;
STICKLINE(起爆点,30,10,4,1),COLORMAGENTA;
XXG:=CROSS(起爆点0,20)||CROSS(起爆点0,18);
ZDF:=(C-REF(C,1))/REF(C,1)*100;
STICKLINE(ZDF<-9.9,O,C,1,0),COLORBLUE;
AQ1:=REF(V,1);AQ2:=DVOL;AQ3:=AQ2/AQ1;
LNX:=AQ3-REF(AQ3,1);E1:=REF(C,1);E2:=DCLOSE;E3:=(E2-E1)/E1*100;
QMX:=E3-REF(E3,1);E:=CROSS(LNX,500)&&CROSS(QMX,10);
STICKLINE(XXG,6,2,2.5,1),COLORYELLOW;STICKLINE(XXG,2,36,3.5,1),COLORYELLOW;
STICKLINE(XXG,10,20,3,1),COLORMAGENTA;DRAWICON(XXG,21,32);DRAWICON(XXG,25,26);
DRAWICON(XXG,30,28);DRAWICON(XXG,18,28);DRAWICON(XXG,15,28);DRAWICON(XXG,20,10);
DRAWICON(XXG,10,28);DRAWICON(XXG,6.6,29);DRAWTEXT(XXG,26,'【启动点】'),COLORMAGENTA;
STICKLINE(IF(E,-10,0),+38,3,2.2,0),COLOR990099;
STICKLINE(IF(E,-120,0),+39,0,2.6,0),COLORBB00BB;
STICKLINE(IF(E,-120,0),+39,0,2.1,0),COLORDD00DD;
STICKLINE(IF(E,-120,0),+36,0,3.0,0),COLOR003300;
STICKLINE(IF(E,-120,0),+36,0,2.4,0),COLOR005500;
STICKLINE(IF(E,-120,0),+33,0,3.0,0),COLOR005555;
STICKLINE(IF(E,-120,0),+33,0,3.4,0),COLOR007777;
STICKLINE(IF(E,-120,0),+33,0,1.8,0),COLOR009999;
STICKLINE(IF(E,-120,0),+33,0,1.2,0),COLOR00BBBB;
STICKLINE(IF(E,-120,0),+33,0,0.6,0),COLOR00DDDD;
STICKLINE(E,2,20,3.5,1),COLORYELLOW;
DRAWTEXT(E,6,'【启爆器】'),COLORYELLOW;


