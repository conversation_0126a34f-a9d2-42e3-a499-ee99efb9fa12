SJKS:=DATE>=1220816;
SJJS:=DATE<=1250801;
SJTJ:= SJKS AND SJJS;
AA:=LLV(LOW,34);
BB:=HHV(HIGH,30);
DD:=(EMA(((CLOSE-AA)/(BB-AA))*(4),4))*(25);
谷:=(((DD-LLV(DD,21))/(HHV(DD,21)-LLV(DD,21)))*(4))*(25);
GUP1:=(CLOSE-LLV(LOW,36))/(HHV(HIGH,36)-LLV(LOW,36))*100;
GUP2:=SMA(GUP1,3,1);
GUP3:=SMA(GUP2,3,1);
GUP4:=SMA(GUP3,3,1);
波:=GUP3,COLORWHITE;
段:=GUP4,COLORYELLOW;
GUP7:=CROSS(GUP4,GUP3) AND GUP3>80;
STICKLINE(FILTER(GUP7,5)=1,0,70,1,0),COLORLIGREEN ;
DRAWTEXT(FILTER(GUP7,5)=1,50,' 卖'),LINETHICK1,COLORFFCC66;
趋势1:=3*SMA((CLOSE-LLV(LOW,27))/(HHV(HIGH,27)-LLV(LOW,27))*100,5,1)-2*SMA(SMA((CLOSE-LLV(LOW,27))/(HHV(HIGH,27)-LLV(LOW,27))*100,5,1),3,1),DOTLINE,COLORGRAY;
GUP8:=CROSS(GUP2,GUP3) AND GUP3>80 AND GUP3>GUP4;
GUP9:=CROSS(趋势1,GUP2) AND 趋势1<20 AND 趋势1<段;
分批卖出:STICKLINE(GUP8,50,90,1,0),COLOR008360;
分批买入:STICKLINE(GUP9,40,0,1,0),COLOR000080;
NOTEXT1:90,COLORGRAY,DOTLINE;
NOTEXT:=0,COLORYELLOW ;
GUP01:=EMA((CLOSE-LLV(LOW,25))/(HHV(HIGH,25)-LLV(LOW,25))*100,5);
GUP02:=(HHV(HIGH,25)-LLV(LOW,25))/EMA((CLOSE-LLV(LOW,25)),5);
GUP03:=CROSS(GUP02,GUP01);
GUP04:=REF(GUP02,2)<REF(GUP02,1) AND GUP02<REF(GUP02,1);
GUP05:=COUNT(GUP03,7)>0 AND GUP04;
GUP06:=REF((LOW+OPEN+CLOSE+HIGH)/4,1);
GUP07:=SMA(ABS(LOW-GUP06),13,1)/SMA(MAX(LOW-GUP06,0),10,1);
GUP08:=EMA(GUP07,10);
GUP09:=EMA(C,5);
GUP10:=LLV(LOW,33);
GUP11:=EMA(IF(LOW<=GUP10,GUP09,0),3)*40;
GUP12:=IF(GUP11>100,GUP11*0.312,GUP11);
更多指标关注:1=1,COLORLIMAGENTA;
异动起飞选股:1=1,COLORLIMAGENTA;
微信公众号:1=1,COLORLIMAGENTA;
资金入场:GUP02,COLORRED;
GUB1:=REF(LOW,1);
GUB2:=SMA(ABS(LOW-GUB1),13,1)/SMA(MAX(LOW-GUB1,0),13,1)*4;
GUB3:=EMA(GUB2,13);
GUB4:=LLV(LOW,34);
GUB5:=EMA(IF(LOW<=GUB4,GUB3,0),3);
GUB6:=100-3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)+2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
GUB7:=100-3*SMA((OPEN-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)+2*SMA(SMA((OPEN-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1);
GUB8:=GUB6<REF(GUB7,1) AND VOL>REF(VOL,1) AND CLOSE>REF(CLOSE,1);
散户:STICKLINE(GUB5>REF(GUB5,1),0,GUB5,0.5,0),COLORGREEN;
STICKLINE(GUB8 AND COUNT(GUB8,30)=1 AND SJTJ,0,18,0.5,0),COLORBLUE;
STICKLINE(GUB8 AND COUNT(GUB8,30)=1 AND SJTJ,18,36,0.5,0),COLORFF7700;
STICKLINE(GUB8 AND COUNT(GUB8,30)=1 AND SJTJ,36,60,1,0),COLORYELLOW;
DRAWTEXT(GUB8 AND COUNT(GUB8,30)=1 AND SJTJ,80,'主力进场★'),COLORYELLOW;
RE:=MA(100*(CLOSE-LLV(CLOSE,34))/(HHV(HIGH,34)-LLV(LOW,34)),5)-20;
R12:=(HIGH+LOW+CLOSE)/3;
R13:=(R12-MA(R12,14))/(0.015*AVEDEV(R12,14));
R14:=(R12-MA(R12,70))/(0.015*AVEDEV(R12,70));
R15:=IF(R13>=150 AND R13<200 AND R14>=150 AND R14<200,10,0);
R16:=IF(R13<=-150 AND R13>-200 AND R14<=-150 AND R14>-200,-10,R15);
R17:=(CLOSE-MA(CLOSE,13))/MA(CLOSE,13)*100;
R18:=100-ABS(R17);
R19:=IF(R18<90,R18,100);
R1A:=IF(RE>0,RE,0);
R1B:=IF(R14>=200 AND R13>=150,15,IF(R14<=-200 AND R13<=-150,-15,R16))+60;
R1C:=R1A>48 AND R1B>60 AND R19<100;
DRAWTEXT(R1C AND COUNT(R1C,25)=1 AND SJTJ,80,'分批出货'),LINETHICK1,COLORFFCC66;
STICKLINE(R1C AND COUNT(R1C,25)=1 AND SJTJ,90,60,0.5,0),COLORFFCC66;
GUP3AA:=IF((CLOSE>REF(CLOSE,1)),88,0);
GUP4AA:=IF(((CLOSE)/(REF(CLOSE,1))>1.05) AND ((HIGH)/(CLOSE)<1.01) AND (GUP3AA>0),91,0);
金牛:=(FILTER((GUP4AA>90),45)),COLORYELLOW;
STICKLINE(金牛 AND SJTJ,35,0,1.5,0),COLOR000099;
STICKLINE(金牛 AND SJTJ,35,0,1.5,0),COLOR0000BB;
STICKLINE(金牛 AND SJTJ,35,0,1.5,0),COLOR0000DD;
STICKLINE(金牛 AND SJTJ,35,0,0.9,0),COLOR0000FF;
DRAWTEXT(金牛>0 AND SJTJ,23,'金牛暴起'),COLOR00C5FF;
NQ:=9;MQ:=3;
RSVQ:=(CLOSE-LLV(LOW,NQ))/(HHV(HIGH,NQ)-LLV(LOW,NQ))*100;
KQ:=SMA(RSVQ,5,1);
DQ:=SMA(KQ,MQ,1);
JQ:=3*KQ-2*DQ;
VARBQ:=(RSVQ/2+22)*1;
量Q:=EMA(VOL,13);
资金Q:=EMA(AMOUNT,13);
过滤Q:=((资金Q /量Q) / 100);
提纯Q:=(((CLOSE -过滤Q) / 过滤Q) * 100);
黄金Q:=((提纯Q < (0)) AND ZXNH),COLORRED;
低买Q:=IF(黄金Q AND RSVQ<VARBQ-2,50,0),COLORRED,LINETHICK2;
高卖Q:IF(黄金Q AND RSVQ>VARBQ,80,120),COLORGREEN,LINETHICK2;
上涨分界Q:=25;
STICKLINE(低买Q AND SJTJ,0,25,2,0),COLORYELLOW;{0,25指高度};
DRAWTEXT(低买Q/50 AND SJTJ,30,'低买'),COLORMAGENTA;
VAR2A:=LLV(LOW,10);
VAR3A:=HHV(HIGH,25);
动力线:= EMA((CLOSE-VAR2A)/(VAR3A-VAR2A)*4,4);
趋势:MA(动力线,2)*30;
IF(趋势>REF(趋势,1),趋势,DRAWNULL),COLORRED;
IF(趋势<REF(趋势,1),趋势,DRAWNULL),COLORBLUE;
STICKLINE(趋势>REF(趋势,1) ,趋势 ,REF(趋势,1),1,0),COLORRED;
STICKLINE(趋势<=REF(趋势,1) ,趋势 ,REF(趋势,1),1,0),COLOR00FF00;
BBB:=SMA(MAX(CLOSE-REF(C,1),0),5,1)/SMA(ABS(CLOSE-REF(C,1)),5,1)*1000;
HHH:=BBB-LLV(BBB,10);
SS:=(MA(HHH,2)*3+HHH*13)/16;
短线买点:=IF(SS>13,MA(SS,2),SS)/6;
指标:=MACD.DIF>MACD.DEA AND MACD.DIF<0.2 AND MACD.DIF>0;
短线出击:IF(CROSS(短线买点,1) AND (短线买点<30) AND 指标 AND SJTJ,20,0);
DRAWTEXT(短线出击/20 AND SJTJ,22,'短线出击'),COLORCYAN;
X:=MA(AMOUNT,5)/MA(V,5)/100;
VAR13A:=(X-MA(AMOUNT,125)/MA(V,125)/100)/X;
VAR14A:=MA((LLV(L,45)-C)/(HHV(H,45)-LLV(L,45))*100,3);
VAR15A:=C/MA(C,60)-1;
大底:=IF(CROSS(-5,VAR14A) AND VAR13A<-0.4,-0.01,-1);
中底:=IF(CROSS(-5,VAR14A) AND VAR13A<-0.25 AND VAR13A>-0.4,-0.3,-1);
小底:=IF(CROSS(-5,VAR14A) AND VAR13A<-0.15 AND VAR13A>-0.25,-0.6,-1);
减仓:=IF(CROSS(VAR14A,-95) AND VAR15A<0.2 AND VAR15A>=0.1,0.6,1);
中顶:=IF(CROSS(VAR14A,-95) AND VAR15A<0.35 AND VAR15A>=0.2,0.3,1),COLORYELLOW;
大顶:=IF(CROSS(VAR14A,-95) AND VAR15A>=0.35,0,1)COLORRED;
VA2:=V>MA(V,89);
VA3:=EXPMA(C,5);
VA4:=EXPMA(C,29);
VA5:=VA3>VA4;
LC:=REF(CLOSE,1);
RSI1:=SMA(MAX(CLOSE-LC,0),12,1)/SMA(ABS(CLOSE-LC),12,1)*100;
RSI2:=SMA(MAX(CLOSE-LC,0),56,1)/SMA(ABS(CLOSE-LC),56,1)*100;
VA6:=RSI1>RSI2 AND VA5 AND VA2;
VASS1:=HHV(H,30);
VASS2:=LLV(L,30);
VASS3:=REF((VASS1/VASS2-1)*100<=30,1);
AK1:=ABS(((3.48*CLOSE+HIGH+LOW)/4-EMA(CLOSE,23))/EMA(CLOSE,23));
AK2:=DMA(((2.15*CLOSE+LOW+HIGH)/4),AK1);
游资:=EMA(AK2,200)*1.1;
妖股:CROSS(C,游资) AND REF(C*1.097,1)<C AND VA6 AND VASS3;
IF(妖股 AND SJTJ,35,0),COLORMAGENTA,LINETHICK3;
DRAWTEXT(妖股 AND SJTJ,22,'妖股'),COLORYELLOW;
C1:=((MA(C,30)-L)/MA(C,60))*200;
M2:=SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100;
G1:=FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5);
TU:=C/MA(C,40)<0.74;
SMMA:=EMA(EMA(C,5),5);
IM:=EMA(C,5)- REF(EMA(C,5),1);
TSMMA:=SMMA - REF(SMMA,1);
DIVMA:= ABS(EMA(C,5)- SMMA);
TDJ:=(H-L)/REF(C,1)>0.05;
ET:=(IM+TSMMA)/2;
TDF:= POW(DIVMA,1)*POW(ET,3);
NTDF:=TDF/HHV(ABS(TDF),5*3);
YUL:=COUNT(TDJ,5)>1;
启动:=TU AND TDJ AND YUL;
确定:=CROSS(NTDF,-0.9);
波段:=FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(启动,1),10);
选股:=FILTER(REF(启动,1) AND (确定 OR C>REF(C,1)) AND MACD.MACD>-1.5,10);
妖底确定:(COUNT(选股,13)>=1 AND 波段)*10,LINETHICK1;
DRAWTEXT(妖底确定/10,7,'妖底确定'),COLORRED;
A:=REF(C,4)>REF(C,3);
A1:=REF(C,3)>REF(C,2);
A2:=REF(C,2)>REF(C,1);
A3:=A AND A1 AND A2;
A5:=BARSLAST(C/REF(C,1)>1.065);
A6:=A5>6 AND C>REF(C,1)*1.03 ;
B:=EMA(MAX(C-REF(C,1),0),5)/EMA(ABS(C-REF(C,1)),5)*100;
B1:=EMA(MAX(C-REF(C,1),0),8)/EMA(ABS(C-REF(C,1)),8)*100;
B2:=REF(C,3)>REF(C,2) AND REF(C,2)>REF(C,1) AND REF(C,4)>REF(C,3);
B3:=CROSS(B,20) AND (CROSS(B,B1));
XGG:=A3 AND A6 AND B3 ;
D3:=C/REF(C,1)<MA(C,24);
D4:=CROSS(C,MA(C,24)) AND V/REF(MA(V,5),1)>1.016;
XG1:=D3 AND D4 AND RANGE(MA(C,24),L,C);
超牛:(XG1 AND XGG)*10;
DRAWTEXT(超牛/10 AND SJTJ,9,'牛股启动'),COLORWHITE;
MAH:=(H*18+REF(H,1)*17+REF(H,2)*16+REF(H,3)*15+REF(H,4)*14+REF(H,5)*13+REF(H,6)*12+REF(H,7)*11+REF(H,8)*10+REF(H,9)*9+REF(H,10)*8+REF(H,11)*7+REF(H,12)*6+REF(H,13)*5+REF(H,14)*4+REF(H,15)*3+REF(H,16)*2+REF(H,17)*1)/171;
MAL:=(L*18+REF(L,1)*17+REF(L,2)*16+REF(L,3)*15+REF(L,4)*14+REF(L,5)*13+REF(L,6)*12+REF(L,7)*11+REF(L,8)*10+REF(L,9)*9+REF(L,10)*8+REF(L,11)*7+REF(L,12)*6+REF(L,13)*5+REF(L,14)*4+REF(L,15)*3+REF(L,16)*2+REF(L,17)*1)/171;
FA1:=ABS(((3.48*CLOSE+HIGH+LOW)/4-EMA(CLOSE,23))/EMA(CLOSE,23));
FA2:=DMA(((2.15*CLOSE+LOW+HIGH)/4),FA1);
金线王:=EMA(FA2,200)*1.118;
金线王2:=EMA(FA2,200)*1.118;
条件:=(C-REF(C,1))/REF(C,1)*100>8;
金K线:=CROSS(C,金线王) AND 条件;
黑马启动:金K线*11,LINETHICK2;
DRAWTEXT(金K线 AND SJTJ,7,'黑马启动'),COLORRED;
DRAWICON(金K线,5,11);